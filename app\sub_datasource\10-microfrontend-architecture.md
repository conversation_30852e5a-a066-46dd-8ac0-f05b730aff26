# 微前端架构设计

## 1. 微前端架构概述

### 1.1 架构目标
- **独立开发**: 数据源管理作为独立的前端应用
- **独立部署**: 可以独立发布和更新
- **技术栈自由**: 使用Vue3技术栈
- **运行时集成**: 通过模块联邦或iframe集成到主应用
- **状态隔离**: 独立的状态管理和路由

### 1.2 微前端方案选择
采用 **Module Federation** 方案，具有以下优势：
- 运行时动态加载
- 共享依赖优化
- 类型安全支持
- 开发体验良好

### 1.3 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    微前端架构                                │
├─────────────────────────────────────────────────────────────┤
│  主应用 (Shell App)                                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              React 主框架                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │  应用编辑器  │  │  用户管理    │  │  工作空间    │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  微前端应用 (Micro Frontend)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              数据源管理应用 (Vue3)                       │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │  数据源列表  │  │  数据源配置  │  │  查询测试    │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  共享服务层 (Shared Services)                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  认证服务    │  │  通信总线    │  │  主题系统    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 2. 技术架构设计

### 2.1 技术栈选择
- **构建工具**: Vite 5.0+ (支持Module Federation)
- **前端框架**: Vue 3.4+ (Composition API)
- **状态管理**: Pinia 2.0+
- **UI组件库**: Element Plus 2.4+
- **路由管理**: Vue Router 4.0+
- **HTTP客户端**: Axios 1.6+
- **类型支持**: TypeScript 5.0+

### 2.2 Module Federation配置
```javascript
// vite.config.js - 数据源微前端应用
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { federation } from '@originjs/vite-plugin-federation'

export default defineConfig({
  plugins: [
    vue(),
    federation({
      name: 'datasource-app',
      filename: 'remoteEntry.js',
      exposes: {
        './DatasourceApp': './src/App.vue',
        './DatasourceRoutes': './src/router/index.js',
        './DatasourceStore': './src/stores/index.js'
      },
      shared: {
        vue: {
          singleton: true,
          requiredVersion: '^3.4.0'
        },
        'vue-router': {
          singleton: true,
          requiredVersion: '^4.0.0'
        },
        'element-plus': {
          singleton: true,
          requiredVersion: '^2.4.0'
        }
      }
    })
  ],
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  }
})
```

```javascript
// vite.config.js - 主应用
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { federation } from '@originjs/vite-plugin-federation'

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'shell-app',
      remotes: {
        datasourceApp: 'http://localhost:3001/assets/remoteEntry.js'
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^18.0.0'
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.0.0'
        }
      }
    })
  ]
})
```

## 3. 项目结构设计

### 3.1 数据源微前端应用结构
```
datasource-frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── main.ts                 # 应用入口
│   ├── App.vue                 # 根组件
│   ├── components/             # 组件库
│   │   ├── common/            # 通用组件
│   │   │   ├── LoadingSpinner.vue
│   │   │   ├── ErrorMessage.vue
│   │   │   └── ConfirmDialog.vue
│   │   └── datasource/        # 数据源组件
│   │       ├── DatasourceCard.vue
│   │       ├── DatasourceForm.vue
│   │       ├── ConnectionTest.vue
│   │       ├── QueryTester.vue
│   │       └── StructureViewer.vue
│   ├── views/                 # 页面视图
│   │   ├── DatasourceList.vue
│   │   ├── DatasourceDetail.vue
│   │   ├── DatasourceCreate.vue
│   │   └── PluginManage.vue
│   ├── router/                # 路由配置
│   │   └── index.ts
│   ├── stores/                # 状态管理
│   │   ├── index.ts
│   │   ├── datasource.ts
│   │   ├── plugin.ts
│   │   └── system.ts
│   ├── composables/           # 组合式API
│   │   ├── useDatasource.ts
│   │   ├── useConnection.ts
│   │   ├── useValidation.ts
│   │   └── useNotification.ts
│   ├── api/                   # API接口
│   │   ├── index.ts
│   │   ├── datasource.ts
│   │   ├── plugin.ts
│   │   └── types.ts
│   ├── utils/                 # 工具函数
│   │   ├── request.ts
│   │   ├── helpers.ts
│   │   └── constants.ts
│   ├── styles/                # 样式文件
│   │   ├── main.scss
│   │   ├── variables.scss
│   │   └── components.scss
│   └── types/                 # 类型定义
│       ├── datasource.ts
│       ├── plugin.ts
│       └── common.ts
├── tests/                     # 测试文件
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── package.json
├── vite.config.ts
├── tsconfig.json
└── README.md
```

### 3.2 应用入口设计
```typescript
// src/main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import { setupGlobalProperties } from './utils/setup'

// 创建应用实例
const app = createApp(App)

// 安装插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 设置全局属性
setupGlobalProperties(app)

// 挂载应用
app.mount('#app')

// 导出应用实例供主应用使用
export { app, router }
```

```vue
<!-- src/App.vue -->
<template>
  <div id="datasource-app" class="datasource-app">
    <el-config-provider :locale="locale">
      <router-view />
    </el-config-provider>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()
const locale = computed(() => {
  return systemStore.language === 'zh-CN' ? zhCn : undefined
})
</script>

<style lang="scss">
.datasource-app {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>
```

## 4. 路由设计

### 4.1 路由配置
```typescript
// src/router/index.ts
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/datasources',
    name: 'DatasourceLayout',
    component: () => import('@/layouts/DatasourceLayout.vue'),
    children: [
      {
        path: '',
        name: 'DatasourceList',
        component: () => import('@/views/DatasourceList.vue'),
        meta: { title: '数据源列表' }
      },
      {
        path: 'create',
        name: 'DatasourceCreate',
        component: () => import('@/views/DatasourceCreate.vue'),
        meta: { title: '创建数据源' }
      },
      {
        path: ':id',
        name: 'DatasourceDetail',
        component: () => import('@/views/DatasourceDetail.vue'),
        meta: { title: '数据源详情' }
      },
      {
        path: ':id/edit',
        name: 'DatasourceEdit',
        component: () => import('@/views/DatasourceEdit.vue'),
        meta: { title: '编辑数据源' }
      }
    ]
  },
  {
    path: '/plugins',
    name: 'PluginManage',
    component: () => import('@/views/PluginManage.vue'),
    meta: { title: '插件管理' }
  }
]

const router = createRouter({
  history: createWebHistory('/datasource-app/'),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (!authStore.isAuthenticated) {
    // 通知主应用需要登录
    window.parent.postMessage({
      type: 'REQUIRE_AUTH',
      payload: { redirectTo: to.fullPath }
    }, '*')
    return
  }
  
  next()
})

export default router
```

## 5. 状态管理设计

### 5.1 Pinia Store设计
```typescript
// src/stores/datasource.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { datasourceApi } from '@/api/datasource'
import type { Datasource, DatasourceCreate, DatasourceUpdate } from '@/types/datasource'

export const useDatasourceStore = defineStore('datasource', () => {
  // 状态
  const datasources = ref<Datasource[]>([])
  const currentDatasource = ref<Datasource | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 过滤器
  const filters = ref({
    type: '',
    status: '',
    search: ''
  })
  
  // 计算属性
  const filteredDatasources = computed(() => {
    return datasources.value.filter(ds => {
      const matchesType = !filters.value.type || ds.pluginId === filters.value.type
      const matchesStatus = !filters.value.status || 
        (filters.value.status === 'valid' ? ds.isValid : !ds.isValid)
      const matchesSearch = !filters.value.search || 
        ds.name.toLowerCase().includes(filters.value.search.toLowerCase())
      
      return matchesType && matchesStatus && matchesSearch
    })
  })
  
  const datasourcesByType = computed(() => {
    return datasources.value.reduce((acc, ds) => {
      if (!acc[ds.pluginId]) acc[ds.pluginId] = []
      acc[ds.pluginId].push(ds)
      return acc
    }, {} as Record<string, Datasource[]>)
  })
  
  // 操作方法
  const fetchDatasources = async (workspaceId: string) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await datasourceApi.list({ workspaceId })
      datasources.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取数据源列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const createDatasource = async (datasourceData: DatasourceCreate) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await datasourceApi.create(datasourceData)
      datasources.value.push(response.data)
      return response.data
    } catch (err: any) {
      error.value = err.message || '创建数据源失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const updateDatasource = async (id: string, updateData: DatasourceUpdate) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await datasourceApi.update(id, updateData)
      const index = datasources.value.findIndex(ds => ds.id === id)
      if (index !== -1) {
        datasources.value[index] = response.data
      }
      return response.data
    } catch (err: any) {
      error.value = err.message || '更新数据源失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const deleteDatasource = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      await datasourceApi.delete(id)
      const index = datasources.value.findIndex(ds => ds.id === id)
      if (index !== -1) {
        datasources.value.splice(index, 1)
      }
    } catch (err: any) {
      error.value = err.message || '删除数据源失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const testConnection = async (id: string) => {
    try {
      const response = await datasourceApi.testConnection(id)
      
      // 更新数据源状态
      const datasource = datasources.value.find(ds => ds.id === id)
      if (datasource) {
        datasource.isValid = response.data.success
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.message || '连接测试失败'
      throw err
    }
  }
  
  const getStructure = async (id: string) => {
    try {
      const response = await datasourceApi.getStructure(id)
      
      // 更新数据源结构
      const datasource = datasources.value.find(ds => ds.id === id)
      if (datasource) {
        datasource.structure = response.data
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取结构失败'
      throw err
    }
  }
  
  const setCurrentDatasource = (datasource: Datasource | null) => {
    currentDatasource.value = datasource
  }
  
  const updateFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters }
  }
  
  const clearError = () => {
    error.value = null
  }
  
  return {
    // 状态
    datasources,
    currentDatasource,
    loading,
    error,
    filters,
    
    // 计算属性
    filteredDatasources,
    datasourcesByType,
    
    // 方法
    fetchDatasources,
    createDatasource,
    updateDatasource,
    deleteDatasource,
    testConnection,
    getStructure,
    setCurrentDatasource,
    updateFilters,
    clearError
  }
})
```

## 6. 组件间通信

### 6.1 与主应用通信
```typescript
// src/utils/communication.ts
interface MessagePayload {
  type: string
  payload?: any
}

class MicroFrontendCommunication {
  private listeners: Map<string, Function[]> = new Map()
  
  constructor() {
    // 监听来自主应用的消息
    window.addEventListener('message', this.handleMessage.bind(this))
  }
  
  // 发送消息到主应用
  sendToParent(type: string, payload?: any) {
    window.parent.postMessage({ type, payload }, '*')
  }
  
  // 监听特定类型的消息
  on(type: string, callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(callback)
  }
  
  // 移除消息监听器
  off(type: string, callback: Function) {
    const callbacks = this.listeners.get(type)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }
  
  // 处理接收到的消息
  private handleMessage(event: MessageEvent) {
    const { type, payload } = event.data as MessagePayload
    const callbacks = this.listeners.get(type)
    
    if (callbacks) {
      callbacks.forEach(callback => callback(payload))
    }
  }
  
  // 通知主应用路由变化
  notifyRouteChange(route: string) {
    this.sendToParent('ROUTE_CHANGE', { route })
  }
  
  // 通知主应用数据源变化
  notifyDatasourceChange(datasource: any) {
    this.sendToParent('DATASOURCE_CHANGE', { datasource })
  }
  
  // 请求主应用的用户信息
  requestUserInfo() {
    this.sendToParent('REQUEST_USER_INFO')
  }
  
  // 请求主应用的工作空间信息
  requestWorkspaceInfo() {
    this.sendToParent('REQUEST_WORKSPACE_INFO')
  }
}

export const communication = new MicroFrontendCommunication()
```

### 6.2 主应用集成代码
```typescript
// 主应用中的微前端集成
import { lazy, Suspense } from 'react'

// 动态导入微前端应用
const DatasourceApp = lazy(() => import('datasourceApp/DatasourceApp'))

function DatasourcePage() {
  useEffect(() => {
    // 监听来自微前端的消息
    const handleMessage = (event: MessageEvent) => {
      const { type, payload } = event.data
      
      switch (type) {
        case 'REQUIRE_AUTH':
          // 处理认证请求
          handleAuthRequest(payload)
          break
        case 'ROUTE_CHANGE':
          // 处理路由变化
          handleRouteChange(payload)
          break
        case 'DATASOURCE_CHANGE':
          // 处理数据源变化
          handleDatasourceChange(payload)
          break
        case 'REQUEST_USER_INFO':
          // 发送用户信息
          sendUserInfo()
          break
        case 'REQUEST_WORKSPACE_INFO':
          // 发送工作空间信息
          sendWorkspaceInfo()
          break
      }
    }
    
    window.addEventListener('message', handleMessage)
    
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])
  
  return (
    <div className="datasource-container">
      <Suspense fallback={<div>Loading...</div>}>
        <DatasourceApp />
      </Suspense>
    </div>
  )
}
```

## 7. 样式和主题

### 7.1 样式隔离
```scss
// src/styles/main.scss
.datasource-app {
  // 使用CSS命名空间隔离样式
  --ds-primary-color: #409eff;
  --ds-success-color: #67c23a;
  --ds-warning-color: #e6a23c;
  --ds-danger-color: #f56c6c;
  --ds-info-color: #909399;
  
  // 重置样式，避免与主应用冲突
  * {
    box-sizing: border-box;
  }
  
  // 组件样式
  .datasource-list {
    padding: 20px;
    
    .datasource-card {
      margin-bottom: 16px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      
      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  .datasource-form {
    .form-section {
      margin-bottom: 24px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #303133;
      }
    }
  }
}
```

### 7.2 主题适配
```typescript
// src/composables/useTheme.ts
import { ref, computed } from 'vue'
import { communication } from '@/utils/communication'

export function useTheme() {
  const theme = ref('light')
  
  // 监听主应用主题变化
  communication.on('THEME_CHANGE', (newTheme: string) => {
    theme.value = newTheme
    updateCSSVariables(newTheme)
  })
  
  const isDark = computed(() => theme.value === 'dark')
  
  const updateCSSVariables = (themeName: string) => {
    const root = document.documentElement
    
    if (themeName === 'dark') {
      root.style.setProperty('--ds-bg-color', '#1a1a1a')
      root.style.setProperty('--ds-text-color', '#ffffff')
      root.style.setProperty('--ds-border-color', '#333333')
    } else {
      root.style.setProperty('--ds-bg-color', '#ffffff')
      root.style.setProperty('--ds-text-color', '#303133')
      root.style.setProperty('--ds-border-color', '#ebeef5')
    }
  }
  
  return {
    theme,
    isDark,
    updateCSSVariables
  }
}
```

## 8. 部署和构建

### 8.1 构建配置
```json
{
  "scripts": {
    "dev": "vite --port 3001",
    "build": "vue-tsc && vite build",
    "preview": "vite preview --port 3001",
    "build:analyze": "vite build --mode analyze",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "type-check": "vue-tsc --noEmit"
  }
}
```

### 8.2 Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 设置CORS头部
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```
