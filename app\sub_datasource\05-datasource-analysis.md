# PagePlug 数据源模块详细分析

## 1. 数据源模块概述

数据源模块是 PagePlug 的核心功能之一，负责管理各种外部数据源的连接、配置、查询执行和结果处理。该模块采用插件化架构，支持多种类型的数据源。

### 1.1 支持的数据源类型

#### 1.1.1 关系型数据库
- **MySQL**: 最流行的开源关系型数据库
- **PostgreSQL**: 功能强大的开源对象关系型数据库
- **Oracle**: 企业级商业数据库
- **SQL Server**: 微软的关系型数据库
- **TiDB**: 分布式NewSQL数据库
- **DM (达梦)**: 国产数据库

#### 1.1.2 NoSQL数据库
- **MongoDB**: 文档型数据库
- **Redis**: 内存键值数据库
- **ArangoDB**: 多模型数据库
- **DynamoDB**: AWS的NoSQL数据库

#### 1.1.3 云服务和API
- **Amazon S3**: 对象存储服务
- **Firestore**: Google的NoSQL文档数据库
- **Elasticsearch**: 搜索和分析引擎
- **REST API**: 通用REST接口
- **GraphQL**: 查询语言和运行时

#### 1.1.4 AI和机器学习服务
- **OpenAI**: GPT模型API
- **Anthropic**: Claude模型API
- **Google AI**: Google的AI服务
- **Appsmith AI**: 内置AI功能

#### 1.1.5 其他服务
- **SMTP**: 邮件发送服务
- **AWS Lambda**: 无服务器计算
- **Databricks**: 数据分析平台
- **Snowflake**: 云数据仓库

## 2. 数据源架构设计

### 2.1 插件化架构
```
┌─────────────────────────────────────────────────────────────┐
│                    数据源插件架构                            │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  数据源管理  │  │  查询构建器  │  │  结果处理器  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 数据源服务   │  │ 插件管理器   │  │ 连接池管理   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  插件层 (Plugin Layer)                                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  MySQL插件   │  │ MongoDB插件  │  │ REST API插件 │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  接口层 (Interface Layer)                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              PluginExecutor 统一接口                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心接口定义
```java
public interface PluginExecutor<T> {
    // 创建数据源连接
    Mono<T> datasourceCreate(DatasourceConfiguration datasourceConfiguration);
    
    // 销毁数据源连接
    void datasourceDestroy(T connection);
    
    // 执行查询/操作
    Mono<ActionExecutionResult> execute(T connection, 
                                       DatasourceConfiguration datasourceConfiguration,
                                       ActionConfiguration actionConfiguration);
    
    // 测试数据源连接
    Mono<DatasourceTestResult> testDatasource(DatasourceConfiguration datasourceConfiguration);
    
    // 获取数据源结构
    Mono<DatasourceStructure> getStructure(T connection, 
                                           DatasourceConfiguration datasourceConfiguration);
    
    // 验证数据源配置
    Set<String> validateDatasource(DatasourceConfiguration datasourceConfiguration);
}
```

## 3. 数据源配置模型

### 3.1 数据源实体设计
```java
@Document(collection = "datasources")
@Data
public class Datasource extends BaseDomain {
    @NotEmpty
    private String name;                    // 数据源名称
    
    @NotEmpty
    private String pluginId;               // 插件ID
    
    @NotEmpty
    private String workspaceId;            // 工作空间ID
    
    private DatasourceConfiguration datasourceConfiguration;  // 数据源配置
    
    private DatasourceStructure structure; // 数据源结构信息
    
    private Boolean isValid;               // 是否有效
    
    private List<String> invalids;         // 无效字段列表
    
    private List<String> messages;         // 消息列表
    
    private Boolean isConfigured;          // 是否已配置
    
    private String toastMessage;           // 提示消息
    
    // 多环境支持
    private Map<String, DatasourceStorage> datasourceStorages;
}
```

### 3.2 数据源配置结构
```java
@Data
public class DatasourceConfiguration {
    private String url;                           // 连接URL
    private DatasourceAuthentication authentication; // 认证信息
    private List<Property> properties;            // 自定义属性
    private List<Property> headers;               // HTTP头部
    private List<Property> queryParameters;       // 查询参数
    private String databaseName;                  // 数据库名称
    private DatasourceConnection connection;      // 连接配置
}

@Data
public class DatasourceAuthentication {
    private String authType;        // 认证类型: basic, oauth, apikey, bearer
    private String username;        // 用户名
    private String password;        // 密码
    private String bearerToken;     // Bearer Token
    private String apiKey;          // API密钥
    private String headerPrefix;    // 头部前缀
    private String value;           // 认证值
    private String addTo;           // 添加位置: header, query
    private Boolean isAuthorized;   // 是否已授权
    private String scopeString;     // OAuth范围
}

@Data
public class DatasourceConnection {
    private DatasourceConnectionMode mode; // 连接模式
    private SSL ssl;                       // SSL配置
    
    @Data
    public static class SSL {
        private Boolean enabled;           // 是否启用SSL
        private String certificateFile;    // 证书文件
        private String keyFile;           // 密钥文件
        private String caCertificateFile; // CA证书文件
    }
}
```

### 3.3 数据源结构模型
```java
@Data
public class DatasourceStructure {
    private List<DatasourceTable> tables;  // 表列表
    private APIResponseError error;        // 错误信息
    
    @Data
    public static class DatasourceTable {
        private String type;                    // 表类型: TABLE, VIEW, COLLECTION
        private String name;                    // 表名
        private List<DatasourceColumn> columns; // 列信息
        private List<DatasourceKey> keys;       // 键信息
        private List<QueryTemplate> templates;  // 查询模板
    }
    
    @Data
    public static class DatasourceColumn {
        private String name;           // 列名
        private String type;           // 数据类型
        private Boolean isNullable;    // 是否可为空
        private String defaultValue;   // 默认值
        private Integer maxLength;     // 最大长度
        private Boolean isPrimaryKey;  // 是否主键
        private Boolean isAutoIncrement; // 是否自增
    }
    
    @Data
    public static class DatasourceKey {
        private String name;              // 键名
        private String type;              // 键类型: PRIMARY, FOREIGN, INDEX
        private List<String> columnNames; // 包含的列名
        private String referencedTable;   // 引用的表(外键)
        private List<String> referencedColumns; // 引用的列(外键)
    }
}
```

## 4. 插件实现分析

### 4.1 MySQL插件实现
**位置**: `app/server/appsmith-plugins/mysqlPlugin/`

```java
@Extension
public static class MySqlPluginExecutor implements PluginExecutor<HikariDataSource>, SmartSubstitutionInterface {
    
    private static final String JDBC_DRIVER = "com.mysql.cj.jdbc.Driver";
    
    @Override
    public Mono<HikariDataSource> datasourceCreate(DatasourceConfiguration datasourceConfiguration) {
        try {
            Class.forName(JDBC_DRIVER);
        } catch (ClassNotFoundException e) {
            return Mono.error(new AppsmithPluginException(
                MySqlPluginError.MYSQL_PLUGIN_ERROR,
                "MySQL JDBC driver not found"));
        }
        
        return Mono.fromCallable(() -> createConnectionPool(datasourceConfiguration))
            .subscribeOn(Schedulers.boundedElastic());
    }
    
    private HikariDataSource createConnectionPool(DatasourceConfiguration datasourceConfiguration) {
        HikariConfig config = new HikariConfig();
        
        // 构建JDBC URL
        String jdbcUrl = buildJdbcUrl(datasourceConfiguration);
        config.setJdbcUrl(jdbcUrl);
        
        // 设置认证信息
        DatasourceAuthentication auth = datasourceConfiguration.getAuthentication();
        if (auth != null) {
            config.setUsername(auth.getUsername());
            config.setPassword(auth.getPassword());
        }
        
        // 连接池配置
        config.setMaximumPoolSize(20);
        config.setMinimumIdle(5);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        // SSL配置
        configureSSL(config, datasourceConfiguration);
        
        return new HikariDataSource(config);
    }
    
    @Override
    public Mono<ActionExecutionResult> execute(HikariDataSource connection,
                                              DatasourceConfiguration datasourceConfiguration,
                                              ActionConfiguration actionConfiguration) {
        
        String query = actionConfiguration.getBody();
        List<Property> requestParams = actionConfiguration.getPluginSpecifiedTemplates();
        
        return Mono.fromCallable(() -> {
            try (Connection conn = connection.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(query)) {
                
                // 参数绑定
                setParameters(stmt, requestParams);
                
                // 执行查询
                if (isSelectQuery(query)) {
                    ResultSet resultSet = stmt.executeQuery();
                    return processResultSet(resultSet);
                } else {
                    int updateCount = stmt.executeUpdate();
                    return createUpdateResult(updateCount);
                }
                
            } catch (SQLException e) {
                throw new AppsmithPluginException(MySqlPluginError.QUERY_EXECUTION_FAILED, e.getMessage());
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    @Override
    public Mono<DatasourceStructure> getStructure(HikariDataSource connection,
                                                  DatasourceConfiguration datasourceConfiguration) {
        return Mono.fromCallable(() -> {
            try (Connection conn = connection.getConnection()) {
                DatabaseMetaData metaData = conn.getMetaData();
                String databaseName = datasourceConfiguration.getDatabaseName();
                
                List<DatasourceTable> tables = new ArrayList<>();
                
                // 获取表信息
                ResultSet tablesResultSet = metaData.getTables(databaseName, null, "%", 
                    new String[]{"TABLE", "VIEW"});
                
                while (tablesResultSet.next()) {
                    String tableName = tablesResultSet.getString("TABLE_NAME");
                    String tableType = tablesResultSet.getString("TABLE_TYPE");
                    
                    DatasourceTable table = new DatasourceTable();
                    table.setName(tableName);
                    table.setType(tableType);
                    
                    // 获取列信息
                    table.setColumns(getTableColumns(metaData, databaseName, tableName));
                    
                    // 获取键信息
                    table.setKeys(getTableKeys(metaData, databaseName, tableName));
                    
                    // 生成查询模板
                    table.setTemplates(generateQueryTemplates(table));
                    
                    tables.add(table);
                }
                
                DatasourceStructure structure = new DatasourceStructure();
                structure.setTables(tables);
                return structure;
                
            } catch (SQLException e) {
                throw new AppsmithPluginException(MySqlPluginError.GET_STRUCTURE_FAILED, e.getMessage());
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }
}
```

### 4.2 MongoDB插件实现
**位置**: `app/server/appsmith-plugins/mongoPlugin/`

```java
@Extension
public static class MongoPluginExecutor implements PluginExecutor<MongoClient>, SmartSubstitutionInterface {
    
    @Override
    public Mono<MongoClient> datasourceCreate(DatasourceConfiguration datasourceConfiguration) {
        return Mono.fromCallable(() -> {
            String connectionString = buildConnectionString(datasourceConfiguration);
            
            MongoClientSettings.Builder settingsBuilder = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(connectionString))
                .applyToConnectionPoolSettings(builder -> 
                    builder.maxSize(20)
                           .minSize(5)
                           .maxConnectionIdleTime(30, TimeUnit.MINUTES)
                           .maxConnectionLifeTime(60, TimeUnit.MINUTES));
            
            // SSL配置
            configureSSL(settingsBuilder, datasourceConfiguration);
            
            return MongoClients.create(settingsBuilder.build());
            
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    @Override
    public Mono<ActionExecutionResult> execute(MongoClient connection,
                                              DatasourceConfiguration datasourceConfiguration,
                                              ActionConfiguration actionConfiguration) {
        
        return Mono.fromCallable(() -> {
            String databaseName = datasourceConfiguration.getDatabaseName();
            MongoDatabase database = connection.getDatabase(databaseName);
            
            // 解析MongoDB命令
            MongoCommand command = parseCommand(actionConfiguration.getBody());
            
            switch (command.getType()) {
                case FIND:
                    return executeFindCommand(database, command);
                case INSERT:
                    return executeInsertCommand(database, command);
                case UPDATE:
                    return executeUpdateCommand(database, command);
                case DELETE:
                    return executeDeleteCommand(database, command);
                case AGGREGATE:
                    return executeAggregateCommand(database, command);
                default:
                    throw new AppsmithPluginException(MongoPluginError.UNSUPPORTED_OPERATION);
            }
            
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    @Override
    public Mono<DatasourceStructure> getStructure(MongoClient connection,
                                                  DatasourceConfiguration datasourceConfiguration) {
        return Mono.fromCallable(() -> {
            String databaseName = datasourceConfiguration.getDatabaseName();
            MongoDatabase database = connection.getDatabase(databaseName);
            
            List<DatasourceTable> collections = new ArrayList<>();
            
            // 获取集合列表
            for (String collectionName : database.listCollectionNames()) {
                MongoCollection<Document> collection = database.getCollection(collectionName);
                
                DatasourceTable table = new DatasourceTable();
                table.setName(collectionName);
                table.setType("COLLECTION");
                
                // 分析文档结构
                table.setColumns(analyzeDocumentStructure(collection));
                
                // 生成查询模板
                table.setTemplates(generateMongoQueryTemplates(collectionName));
                
                collections.add(table);
            }
            
            DatasourceStructure structure = new DatasourceStructure();
            structure.setTables(collections);
            return structure;
            
        }).subscribeOn(Schedulers.boundedElastic());
    }
}
```

### 4.3 REST API插件实现
**位置**: `app/server/appsmith-plugins/restApiPlugin/`

```java
@Extension
public static class RestApiPluginExecutor implements PluginExecutor<Void>, SmartSubstitutionInterface {
    
    private final WebClient webClient;
    
    public RestApiPluginExecutor() {
        this.webClient = WebClient.builder()
            .clientConnector(new ReactorClientHttpConnector(
                HttpClient.create()
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
                    .responseTimeout(Duration.ofSeconds(30))
            ))
            .build();
    }
    
    @Override
    public Mono<Void> datasourceCreate(DatasourceConfiguration datasourceConfiguration) {
        // REST API不需要持久连接
        return Mono.empty();
    }
    
    @Override
    public Mono<ActionExecutionResult> execute(Void connection,
                                              DatasourceConfiguration datasourceConfiguration,
                                              ActionConfiguration actionConfiguration) {
        
        // 构建请求
        String url = buildRequestUrl(datasourceConfiguration, actionConfiguration);
        HttpMethod method = HttpMethod.valueOf(actionConfiguration.getHttpMethod());
        
        WebClient.RequestBodySpec request = webClient.method(method).uri(url);
        
        // 添加头部
        addHeaders(request, datasourceConfiguration, actionConfiguration);
        
        // 添加认证
        addAuthentication(request, datasourceConfiguration);
        
        // 添加请求体
        if (hasRequestBody(method)) {
            request.bodyValue(actionConfiguration.getBody());
        }
        
        // 执行请求
        return request.retrieve()
            .toEntity(String.class)
            .map(this::processResponse)
            .onErrorMap(this::mapError);
    }
    
    private ActionExecutionResult processResponse(ResponseEntity<String> response) {
        ActionExecutionResult result = new ActionExecutionResult();
        result.setIsExecutionSuccess(true);
        result.setStatusCode(String.valueOf(response.getStatusCodeValue()));
        result.setHeaders(response.getHeaders().toSingleValueMap());
        
        // 解析响应体
        String body = response.getBody();
        if (body != null) {
            try {
                // 尝试解析为JSON
                Object parsedBody = objectMapper.readValue(body, Object.class);
                result.setBody(parsedBody);
            } catch (Exception e) {
                // 如果不是JSON，直接返回字符串
                result.setBody(body);
            }
        }
        
        return result;
    }
}
```

## 5. 数据源管理服务

### 5.1 数据源服务实现
```java
@Service
@Slf4j
public class DatasourceServiceImpl implements DatasourceService {
    
    private final DatasourceRepository repository;
    private final PluginService pluginService;
    private final DatasourceContextService contextService;
    private final DatasourceCacheService cacheService;
    
    @Override
    public Mono<Datasource> create(Datasource datasource) {
        return validateDatasource(datasource)
            .flatMap(this::enrichWithDefaults)
            .flatMap(repository::save)
            .flatMap(this::initializeStructure)
            .doOnSuccess(ds -> publishEvent(new DatasourceCreatedEvent(ds)))
            .doOnError(error -> log.error("Failed to create datasource: {}", datasource.getName(), error));
    }
    
    @Override
    public Mono<DatasourceTestResult> testDatasource(String datasourceId) {
        return repository.findById(datasourceId)
            .switchIfEmpty(Mono.error(new AppsmithException(AppsmithError.NO_RESOURCE_FOUND)))
            .flatMap(datasource -> {
                return pluginService.getPluginExecutor(datasource.getPluginId())
                    .flatMap(executor -> executor.testDatasource(datasource.getDatasourceConfiguration()))
                    .doOnSuccess(result -> cacheTestResult(datasourceId, result));
            })
            .timeout(Duration.ofSeconds(30))
            .onErrorMap(TimeoutException.class, ex -> 
                new AppsmithException(AppsmithError.PLUGIN_EXECUTION_TIMEOUT));
    }
    
    @Override
    public Mono<DatasourceStructure> getStructure(String datasourceId) {
        return cacheService.getStructure(datasourceId)
            .switchIfEmpty(fetchAndCacheStructure(datasourceId));
    }
    
    private Mono<DatasourceStructure> fetchAndCacheStructure(String datasourceId) {
        return repository.findById(datasourceId)
            .flatMap(datasource -> contextService.getDatasourceContext(datasource))
            .flatMap(context -> {
                PluginExecutor executor = context.getPluginExecutor();
                return executor.getStructure(context.getConnection(), context.getDatasourceConfiguration());
            })
            .doOnSuccess(structure -> cacheService.cacheStructure(datasourceId, structure));
    }
}
```

### 5.2 连接池管理
```java
@Service
public class ConnectionPoolManager {
    
    private final Map<String, Object> connectionPools = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    @PostConstruct
    public void init() {
        // 定期清理无效连接
        scheduler.scheduleAtFixedRate(this::cleanupInvalidConnections, 5, 5, TimeUnit.MINUTES);
        
        // 定期检查连接健康状态
        scheduler.scheduleAtFixedRate(this::healthCheck, 1, 1, TimeUnit.MINUTES);
    }
    
    public <T> Mono<T> getConnection(String datasourceId, DatasourceConfiguration config, 
                                    PluginExecutor<T> executor) {
        return Mono.fromCallable(() -> {
            @SuppressWarnings("unchecked")
            T connection = (T) connectionPools.get(datasourceId);
            
            if (connection == null || !isConnectionValid(connection)) {
                // 创建新连接
                connection = executor.datasourceCreate(config).block();
                connectionPools.put(datasourceId, connection);
            }
            
            return connection;
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    public void releaseConnection(String datasourceId) {
        Object connection = connectionPools.remove(datasourceId);
        if (connection != null) {
            // 销毁连接
            destroyConnection(connection);
        }
    }
    
    private void cleanupInvalidConnections() {
        connectionPools.entrySet().removeIf(entry -> {
            if (!isConnectionValid(entry.getValue())) {
                destroyConnection(entry.getValue());
                return true;
            }
            return false;
        });
    }
}
```

## 6. 查询执行引擎

### 6.1 查询执行服务
```java
@Service
public class QueryExecutionService {
    
    private final DatasourceService datasourceService;
    private final PluginService pluginService;
    private final ConnectionPoolManager connectionManager;
    
    public Mono<ActionExecutionResult> executeQuery(String datasourceId, ActionConfiguration actionConfig) {
        return datasourceService.findById(datasourceId)
            .flatMap(datasource -> executeWithDatasource(datasource, actionConfig))
            .doOnSuccess(result -> logQueryExecution(datasourceId, actionConfig, result))
            .doOnError(error -> logQueryError(datasourceId, actionConfig, error));
    }
    
    private Mono<ActionExecutionResult> executeWithDatasource(Datasource datasource, 
                                                             ActionConfiguration actionConfig) {
        return pluginService.getPluginExecutor(datasource.getPluginId())
            .flatMap(executor -> {
                return connectionManager.getConnection(datasource.getId(), 
                                                     datasource.getDatasourceConfiguration(), 
                                                     executor)
                    .flatMap(connection -> {
                        return executor.execute(connection, 
                                              datasource.getDatasourceConfiguration(), 
                                              actionConfig);
                    });
            })
            .timeout(Duration.ofSeconds(60))
            .onErrorMap(this::mapExecutionError);
    }
    
    private ActionExecutionResult mapExecutionError(Throwable error) {
        if (error instanceof TimeoutException) {
            return ActionExecutionResult.builder()
                .isExecutionSuccess(false)
                .errorInfo(new ActionExecutionError("QUERY_TIMEOUT", "Query execution timed out"))
                .build();
        } else if (error instanceof SQLException) {
            return ActionExecutionResult.builder()
                .isExecutionSuccess(false)
                .errorInfo(new ActionExecutionError("SQL_ERROR", error.getMessage()))
                .build();
        } else {
            return ActionExecutionResult.builder()
                .isExecutionSuccess(false)
                .errorInfo(new ActionExecutionError("EXECUTION_ERROR", "Query execution failed"))
                .build();
        }
    }
}
```

### 6.2 参数绑定和智能替换
```java
@Component
public class SmartSubstitutionProcessor {
    
    public Object processBindings(Object obj, List<Property> bindings, Map<String, Object> context) {
        if (obj == null) {
            return null;
        }
        
        if (obj instanceof String) {
            return processStringBindings((String) obj, bindings, context);
        } else if (obj instanceof Map) {
            return processMapBindings((Map<?, ?>) obj, bindings, context);
        } else if (obj instanceof List) {
            return processListBindings((List<?>) obj, bindings, context);
        }
        
        return obj;
    }
    
    private String processStringBindings(String str, List<Property> bindings, Map<String, Object> context) {
        String result = str;
        
        // 处理 {{}} 绑定
        Pattern pattern = Pattern.compile("\\{\\{([^}]+)\\}\\}");
        Matcher matcher = pattern.matcher(str);
        
        while (matcher.find()) {
            String bindingExpression = matcher.group(1).trim();
            Object value = evaluateBinding(bindingExpression, bindings, context);
            
            if (value != null) {
                result = result.replace(matcher.group(0), value.toString());
            }
        }
        
        return result;
    }
    
    private Object evaluateBinding(String expression, List<Property> bindings, Map<String, Object> context) {
        // 简单的表达式求值
        if (context.containsKey(expression)) {
            return context.get(expression);
        }
        
        // 查找绑定参数
        for (Property binding : bindings) {
            if (expression.equals(binding.getKey())) {
                return binding.getValue();
            }
        }
        
        return null;
    }
}
```
